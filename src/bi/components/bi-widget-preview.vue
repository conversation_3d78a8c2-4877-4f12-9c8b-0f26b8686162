<script setup>
import { Chart<PERSON>elper } from '@sensehawk/chart-generator';
import { watchDebounced } from '@vueuse/core';
import * as echarts from 'echarts';
import { storeToRefs } from 'pinia';
import { nextTick, onBeforeUnmount } from 'vue';
import { BI_CHART_COLOR_PALETTES } from '~/bi/helpers/bi-helpers';
import { useBiStore } from '~/bi/store/bi.store';
import BiBottomDrawer from './bi-bottom-drawer.vue';

const emit = defineEmits(['continue']);

const bi_store = useBiStore();
const { chart_builder_config, chart_builder_data } = storeToRefs(bi_store);

const state = reactive({
  chart_instance: false,
  echarts_config: null,
});

function renderWidget() {
  if (chart_builder_config.value.chart_type === 'table') {
    // Probably some data processing
  }
  else {
    const helper = new ChartHelper();
    helper.setData(chart_builder_data.value, {
      category: chart_builder_config.value.layout_category,
      values: chart_builder_config.value.layout_values.map(item => item.value),
      // stackBy: 'Quarter',
    });
    helper.setChartType(chart_builder_config.value.chart_type.replace('_chart', ''));

    if (chart_builder_config.value.layout_values) {
      Object.values(chart_builder_config.value.layout_values).forEach((series) => {
        helper.setSeriesType(series.value, series.chart_type);
        helper.setSeriesStyle(series.value, {
          lineStyle: series.line_style,
          lineWidth: Number.parseInt(series.line_width),
          color: 'green',
        });
        helper.setSeriesConfig(
          series.value,
          {
            color: series.color,
            // smooth: [Boolean] not working
            smooth: series.line_shape === 'curved',
            // stack: how to use it?
          },
        );
      });
    }

    // Display tab
    helper.setTitle(chart_builder_config.value.title, chart_builder_config.value.subtitle);
    helper.setLegend({
      show: chart_builder_config.value.legend_position !== 'none',
      position: chart_builder_config.value.legend_position,
      orientation: chart_builder_config.value.legend_position === 'top' || chart_builder_config.value.legend_position === 'bottom' ? 'horizontal' : 'vertical',
    });
    helper.setDataValuesDisplay({
      displayValues: chart_builder_config.value.values === 'show',
    });
    helper.setColors(BI_CHART_COLOR_PALETTES[chart_builder_config.value.color_palette].colors);

    // Axes tab
    helper.setAxisNames(chart_builder_config.value.category_axis_name, chart_builder_config.value.value_axis_name, chart_builder_config.value.secondary_y_axis);
    helper.setDualYAxis(chart_builder_config.value.dual_y_axis);
    helper.setScale({
      primary: chart_builder_config.value.primary_scale,
      secondary: chart_builder_config.value.secondary_scale,
    });
    helper.setAxisTickLabels({
      categoryTickLabels: chart_builder_config.value.category_tick_label,
      valueTickLabels: chart_builder_config.value.value_tick_label,
      secondaryValueTickLabels: chart_builder_config.value.secondary_value_tick_label,
    });
    helper.setAxisRange({
      valueRangeMin: !Number.isNaN(Number.parseInt(chart_builder_config.value.custom_range_min)) ? Number.parseInt(chart_builder_config.value.custom_range_min) : null,
      valueRangeMax: !Number.isNaN(Number.parseInt(chart_builder_config.value.custom_range_max)) ? Number.parseInt(chart_builder_config.value.custom_range_max) : null,
    });

    // Advanced tab
    helper.setAccessibilityPatterns(chart_builder_config.value.accessibility_patterns);
    helper.setOrientation(chart_builder_config.value.orientation);
    if (chart_builder_config.value.data_zoom !== 'disabled') {
      helper.setDataZoom(chart_builder_config.value.data_zoom);
    }
    // helper.setReferenceLines(chart_builder_config.value.reference_lines.map(line => ({
    //   value: line.reference_line,
    //   label: line.reference_line,
    //   color: 'red',
    // })));

    const echarts_config = helper.generateEChartsConfig();

    const chartElement = document.getElementById('chart-container');
    if (!state.chart_instance) {
      state.chart_instance = echarts.init(chartElement);
    }
    else if (state.chart_instance) {
      state.chart_instance.clear();
    }

    state.chart_instance.setOption(echarts_config);
  }
}

watchDebounced(
  () => chart_builder_config.value,
  async () => {
    await nextTick();
    renderWidget();
  },
  { deep: true, immediate: true, debounce: 300 },
);

onBeforeUnmount(() => {
  if (state.chart_instance) {
    state.chart_instance.dispose();
    state.chart_instance = null;
  }
});
</script>

<template>
  <div class="h-full w-full flex flex-col relative">
    <div class="p-6 flex-1">
      <div v-if="chart_builder_config.chart_type === 'table'">
        TABLE
      </div>
      <div v-else id="chart-container" class="h-full w-full" />
    </div>

    <hr>
    <div class="p-6 w-full flex justify-end items-center">
      <HawkButton @click="emit('continue')">
        <IconHawkRocketTwo />
        Publish widget
      </HawkButton>
    </div>

    <BiBottomDrawer
      show-text="Show results"
      hide-text="Hide results"
    >
      <template #default>
        TABLE
      </template>
    </BiBottomDrawer>
  </div>
</template>
