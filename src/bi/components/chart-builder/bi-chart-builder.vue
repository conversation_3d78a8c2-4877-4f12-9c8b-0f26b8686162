<script setup>
import { storeToRefs } from 'pinia';
import { onMounted } from 'vue';
import { useBiStore } from '~/bi/store/bi.store';

const emit = defineEmits(['go-back']);

const bi_store = useBiStore();
const { chart_builder_config } = storeToRefs(bi_store);

const chart_types = [
  ['table', 'Table', IconHawkTableTwo],
  ['bar_chart', 'Bar chart', IconHawkHorizontalBarChartOne],
  ['line_chart', 'Line chart', IconHawkLineChartUpOne],
  ['area_chart', 'Area chart', IconHawkAreaChart],
  ['pie_chart', 'Pie chart', IconHawkPieChartThree],
  ['doughnut_chart', 'Doughnut chart', IconHawkDoughnutChart],
  ['scatter_chart', 'Scatter chart', IconHawkScatterChart],
  ['gauge_chart', 'Gauge chart', IconHawkGaugeChart],
  ['heatmap_chart', 'Heatmap chart', IconHawkHeatmapChart],
  ['pyramid_chart', 'Pyramid chart', IconHawkPyramidChart],
  ['funnel_chart', 'Funnel chart', IconHawkFunnelChart],
  ['pareto_chart', 'Pareto chart', IconHawkParetoChart],
  ['waterfall_chart', 'Waterfall chart', IconHawkWaterfallChart],
  ['timeseries_chart', 'Timeseries chart', IconHawkTimeseriesChart],
].map((item) => {
  return {
    value: item[0],
    label: item[1],
    icon: item[2],
  };
});

const state = reactive({
  form_data: {},
  active_item: 'layout',
});

const tabs = computed(() => {
  const has_display = ['table', 'bar_chart', 'line_chart', 'area_chart', 'pie_chart', 'doughnut_chart', 'scatter_chart', 'heatmap_chart', 'pyramid_chart', 'funnel_chart', 'pareto_chart', 'waterfall_chart', 'timeseries_chart'].includes(state.form_data.chart_type);
  const has_axes = ['bar_chart', 'line_chart', 'area_chart', 'scatter_chart', 'waterfall_chart', 'timeseries_chart'].includes(state.form_data.chart_type);
  const has_advanced = ['bar_chart', 'line_chart', 'area_chart', 'scatter_chart', 'timeseries_chart'].includes(state.form_data.chart_type);
  const has_settings = ['gauge_chart', 'heatmap_chart', 'pyramid_chart', 'funnel_chart', 'pareto_chart', 'waterfall_chart', 'timeseries_chart'].includes(state.form_data.chart_type);

  return [
    { uid: 'layout', label: 'Layout' },
    ...(has_display ? [{ uid: 'display', label: 'Display' }] : []),
    ...(has_axes ? [{ uid: 'axes', label: 'Axes' }] : []),
    ...(has_advanced ? [{ uid: 'advanced', label: 'Advanced' }] : []),
    ...(has_settings ? [{ uid: 'settings', label: 'Settings' }] : []),
  ];
});

function onChartBuilderConfigChange(fields, index) {
  if (state.form_data?.layout_values?.[index]) {
    Object.keys(fields).forEach((field_name) => {
      state.form_data.layout_values[index][field_name] = fields[field_name];
    });
  }
}

watch(() => state.form_data, () => {
  chart_builder_config.value = {
    ...chart_builder_config.value,
    ...state.form_data,
  };
});

onMounted(() => {
  chart_builder_config.value = {
    ...chart_builder_config.value,
    ...state.form_data,
  };
});
</script>

<template>
  <div>
    <div
      class="flex items-center gap-2 cursor-pointer hover:underline text-sm font-semibold text-gray-700"
      @click="emit('go-back')"
    >
      <IconHawkArrowLeft />
      Back to data builder
    </div>

    <Vueform
      v-model="state.form_data"
      size="sm"
      :columns="{
        default: { container: 12, label: 12, wrapper: 12 },
        sm: { container: 12, label: 12, wrapper: 12 },
      }"
      class="mt-6"
    >
      <SelectElement
        name="chart_type"
        default="table"
        :label="$t('Chart type')"
        :items="chart_types"
        :native="false"
        :can-clear="false"
        :can-deselect="false"
        @change="state.active_item = 'layout'"
      >
        <template #option="{ option }">
          <div class="flex items-center gap-2">
            <component :is="option.icon" class="text-gray-500" />
            {{ option.label }}
          </div>
        </template>
        <template #single-label="{ value }">
          <div class="w-full flex items-center gap-2 px-2">
            <component :is="value.icon" class="text-gray-500" />
            {{ value.label }}
          </div>
        </template>
      </SelectElement>
      <HawkTabs v-if="state.form_data.chart_type" :tabs="tabs" :active_item="state.active_item" class="col-span-12 mt-6 mb-3" @tab-click="state.active_item = $event.uid" />
      <div v-show="state.active_item === 'layout'" :key="state.form_data.chart_type" class="col-span-12 flex flex-col gap-y-5">
        <BiChartLayoutTab
          :chart-type="state.form_data.chart_type"
          :chart-config="chart_builder_config"
          @chart-builder-config-change="onChartBuilderConfigChange"
        />
      </div>
      <div v-show="state.active_item === 'display'" :key="state.form_data.chart_type" class="col-span-12 flex flex-col gap-y-5">
        <BiChartDisplayTab
          :chart-type="state.form_data.chart_type"
        />
      </div>
      <div v-show="state.active_item === 'axes'" :key="state.form_data.chart_type" class="col-span-12 flex flex-col gap-y-5">
        <BiChartAxesTab
          :chart-type="state.form_data.chart_type"
          :chart-config="chart_builder_config"
        />
      </div>
      <div v-show="state.active_item === 'advanced'" :key="state.form_data.chart_type" class="col-span-12 flex flex-col gap-y-5">
        <BiChartAdvancedTab
          :chart-type="state.form_data.chart_type"
        />
      </div>
      <div v-show="state.active_item === 'settings'" :key="state.form_data.chart_type" class="col-span-12 flex flex-col gap-y-5">
        <BiChartSettingsTab
          :chart-type="state.form_data.chart_type"
        />
      </div>
    </Vueform>
  </div>
</template>
