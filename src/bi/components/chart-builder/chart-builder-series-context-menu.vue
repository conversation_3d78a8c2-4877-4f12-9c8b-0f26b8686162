<script setup>
const props = defineProps({
  seriesConfig: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['fieldSelected']);

const state = reactive({
  series_name: 'Profit_$ITEMS',
  chart_type: 'line',
  y_axis: 'primary',
  line_style: 'solid',
  line_width: 2,
  line_shape: 'straight',
  legend: 'Profit',
  prefix: '$',
  suffix: 'units',
  stack: false,
});

const series_options = computed(() => [
  { value: 'Profit_$ITEMS', label: 'Profit_$ITEMS' },
  { value: 'Revenue_$ITEMS', label: 'Revenue_$ITEMS' },
  { value: 'Cost_$ITEMS', label: 'Cost_$ITEMS' },
]);

const chart_types = computed(() => {
  return [
    { uid: 'line', leftSlot: IconHawkLineChartUpOne, tooltip_text: 'Line' },
    { uid: 'area', leftSlot: IconHawkAreaChart, tooltip_text: 'Area' },
    { uid: 'bar', leftSlot: IconHawkHorizontalBarChartOne, tooltip_text: 'Bar' },
  ];
});

const y_axis_options = computed(() => [
  { uid: 'primary', label: 'Primary' },
  { uid: 'secondary', label: 'Secondary' },
]);

const line_styles = computed(() => {
  return [
    { uid: 'solid', leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 2px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Solid' },
    { uid: 'dashed', leftSlot: IconHawkStroke, tooltip_text: 'Dashed' },
    { uid: 'dotted', leftSlot: IconHawkDotsHorizontal, tooltip_text: 'Dotted' },
  ];
});

const line_widths = computed(() => {
  return [
    { uid: 1, leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 1px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Thin' },
    { uid: 2, leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 2px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Medium' },
    { uid: 3, leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 3px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Thick' },
  ];
});

const line_shapes = computed(() => {
  return [
    { uid: 'straight', leftSlot: IconHawkSquareLine, tooltip_text: 'Straight' },
    { uid: 'curved', leftSlot: IconHawkSmoothLine, tooltip_text: 'Curved' },
  ];
});

function onFieldSelected(field_name, value) {
  state[field_name] = value;
  emit('fieldSelected', { [field_name]: value });
}

onMounted(() => {
  Object.keys(props.seriesConfig).forEach((key) => {
    state[key] = props.seriesConfig[key];
  });
});
</script>

<template>
  <div class="flex items-center gap-1">
    <HawkMenu position="fixed" additional_trigger_classes="mt-1.5 p-0 m-0 !ring-0 !border-0 focus:!ring-0" additional_dropdown_classes="w-[400px]">
      <template #trigger>
        <IconHawkDotsVertical class="w-4 h-4" />
      </template>
      <template #content>
        <div class="p-6">
          <!-- Header -->
          <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-1">
              Configure series
            </h3>
          </div>

          <Vueform
            size="sm"
            :columns="{
              default: { container: 12, label: 3, wrapper: 9 },
            }"
            class="space-y-6"
          >
            <!-- Series Selection with Color Indicator -->
            <div class="mb-6">
              <div class="flex items-center gap-3">
                <div class="w-3 h-3 rounded-full bg-purple-500" />
                <div class="flex-1">
                  <SelectElement
                    name="series_name"
                    :items="series_options"
                    :native="false"
                    :default="state.series_name"
                    placeholder="Select series"
                    @change="onFieldSelected('series_name', $event)"
                  />
                </div>
              </div>
            </div>

            <!-- Chart type -->
            <div class="flex items-center gap-4">
              <label class="text-sm font-medium text-gray-700 w-20 flex-shrink-0">Chart type</label>
              <HawkButtonGroup
                :items="chart_types"
                icon
                size="sm"
                :active_item="state.chart_type"
                class="w-fit"
                @select="onFieldSelected('chart_type', $event.uid)"
              />
            </div>

            <!-- Y-axis -->
            <div class="flex items-center gap-4">
              <label class="text-sm font-medium text-gray-700 w-20 flex-shrink-0">Y-axis</label>
              <HawkButtonGroup
                :items="y_axis_options"
                size="sm"
                :active_item="state.y_axis"
                class="w-fit"
                @select="onFieldSelected('y_axis', $event.uid)"
              />
            </div>

            <!-- Line style -->
            <div class="flex items-center gap-4">
              <label class="text-sm font-medium text-gray-700 w-20 flex-shrink-0">Line style</label>
              <HawkButtonGroup
                :items="line_styles"
                icon
                size="sm"
                :active_item="state.line_style"
                class="w-fit"
                @select="onFieldSelected('line_style', $event.uid)"
              />
            </div>

            <!-- Line width -->
            <div class="flex items-center gap-4">
              <label class="text-sm font-medium text-gray-700 w-20 flex-shrink-0">Line width</label>
              <HawkButtonGroup
                :items="line_widths"
                icon
                size="sm"
                :active_item="state.line_width"
                class="w-fit"
                @select="onFieldSelected('line_width', $event.uid)"
              />
            </div>

            <!-- Line shape -->
            <div class="flex items-center gap-4">
              <label class="text-sm font-medium text-gray-700 w-20 flex-shrink-0">Line shape</label>
              <HawkButtonGroup
                :items="line_shapes"
                icon
                size="sm"
                :active_item="state.line_shape"
                class="w-fit"
                @select="onFieldSelected('line_shape', $event.uid)"
              />
            </div>

            <!-- Legend -->
            <div class="flex items-center gap-4">
              <label class="text-sm font-medium text-gray-700 w-20 flex-shrink-0">Legend</label>
              <div class="flex-1 max-w-[200px]">
                <TextElement
                  name="legend"
                  :default="state.legend"
                  placeholder="Enter legend text"
                  @change="onFieldSelected('legend', $event)"
                />
              </div>
            </div>

            <!-- Prefix -->
            <div class="flex items-center gap-4">
              <label class="text-sm font-medium text-gray-700 w-20 flex-shrink-0">Prefix</label>
              <div class="flex-1 max-w-[200px]">
                <TextElement
                  name="prefix"
                  :default="state.prefix"
                  placeholder="Enter prefix"
                  @change="onFieldSelected('prefix', $event)"
                />
              </div>
            </div>

            <!-- Suffix -->
            <div class="flex items-center gap-4">
              <label class="text-sm font-medium text-gray-700 w-20 flex-shrink-0">Suffix</label>
              <div class="flex-1 max-w-[200px]">
                <TextElement
                  name="suffix"
                  :default="state.suffix"
                  placeholder="Enter suffix"
                  @change="onFieldSelected('suffix', $event)"
                />
              </div>
            </div>

            <!-- Stack -->
            <div class="flex items-center gap-4">
              <div class="flex items-center gap-2 w-20 flex-shrink-0">
                <label class="text-sm font-medium text-gray-700">Stack</label>
                <IconHawkInfoCircle class="w-4 h-4 text-gray-400" />
              </div>
              <CheckboxElement
                name="stack"
                :default="state.stack"
                @change="onFieldSelected('stack', $event)"
              />
            </div>
          </Vueform>
        </div>
      </template>
    </HawkMenu>
  </div>
</template>
