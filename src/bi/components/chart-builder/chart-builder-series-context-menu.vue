<script setup>
const props = defineProps({
  seriesConfig: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['fieldSelected']);

const state = reactive({
  chart_type: null,
  line_style: null,
  line_width: null,
  line_shape: null,
});

const chart_types = computed(() => {
  return [
    { uid: 'line', leftSlot: IconHawkLineChartUpOne, tooltip_text: 'Line' },
    { uid: 'area', leftSlot: IconHawkAreaChart, tooltip_text: 'Area' },
    { uid: 'bar', leftSlot: IconHawkHorizontalBarChartOne, tooltip_text: 'Bar' },
  ];
});

const line_styles = computed(() => {
  return [
    { uid: 'solid', leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 2px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Solid' },
    { uid: 'dashed', leftSlot: IconHawkStroke, tooltip_text: 'Dashed' },
    { uid: 'dotted', leftSlot: IconHawkDotsHorizontal, tooltip_text: 'Dotted' },
  ];
});

const line_widths = computed(() => {
  return [
    { uid: 1, leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 1px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Thin' },
    { uid: 2, leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 2px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Medium' },
    { uid: 3, leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 3px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Thick' },
  ];
});

const line_shapes = computed(() => {
  return [
    { uid: 'straight', leftSlot: IconHawkSquareLine, tooltip_text: 'Straight' },
    { uid: 'curved', leftSlot: IconHawkSmoothLine, tooltip_text: 'Curved' },
  ];
});

function onFieldSelected(field_name, value) {
  state[field_name] = value;
  emit('fieldSelected', { [field_name]: value });
}

onMounted(() => {
  Object.keys(props.seriesConfig).forEach((key) => {
    state[key] = props.seriesConfig[key];
  });
});
</script>

<template>
  <div class="flex items-center gap-1">
    <HawkMenu position="fixed" additional_trigger_classes="mt-1.5 p-0 m-0 !ring-0 !border-0 focus:!ring-0">
      <template #trigger>
        <IconHawkDotsVertical class="w-4 h-4" />
      </template>
      <template #content>
        <div class="p-4">
          <div class="grid grid-cols-12 gap-x-6">
            <div class="col-span-4">
              Chart type
            </div>
            <div class="col-span-8">
              <HawkButtonGroup
                :items="chart_types"
                icon
                size="sm"
                :active_item="state.chart_type"
                class="w-fit"
                @select="onFieldSelected('chart_type', $event.uid)"
              />
            </div>
            <div class="col-span-4">
              Line style
            </div>
            <div class="col-span-8">
              <HawkButtonGroup
                :items="line_styles"
                icon
                size="sm"
                :active_item="state.line_style"
                class="w-fit"
                @select="onFieldSelected('line_style', $event.uid)"
              />
            </div>
            <div class="col-span-4">
              Line width
            </div>
            <div class="col-span-8">
              <HawkButtonGroup
                :items="line_widths"
                icon
                size="sm"
                :active_item="state.line_width"
                class="w-fit"
                @select="onFieldSelected('line_width', $event.uid)"
              />
            </div>
            <div class="col-span-4">
              Line shape
            </div>
            <div class="col-span-8">
              <HawkButtonGroup
                :items="line_shapes"
                icon
                size="sm"
                :active_item="state.line_shape"
                class="w-fit"
                @select="onFieldSelected('line_shape', $event.uid)"
              />
            </div>
          </div>
        </div>
      </template>
    </HawkMenu>
  </div>
</template>
