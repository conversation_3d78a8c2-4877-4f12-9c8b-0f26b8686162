<script setup>
import { storeToRefs } from 'pinia';
import { useBiStore } from '~/bi/store/bi.store';

const props = defineProps({
  chartType: {
    type: String,
    required: true,
  },
  chartConfig: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['chartBuilderConfigChange']);

const colors = ['#101828', '#004EEB', '#D92D20', '#DC6803', '#039855', '#7839EE', '#4CA30D', '#0E9384', '#BA24D5', '#E31B54', '#344054', '#2E90FA', '#F97066', '#FDB022', '#32D583', '#A48AFB', '#66C61C', '#2ED3B7', '#E478FA', '#FD6F8E', '#667085', '#84CAFF', '#FDA29B', '#FEC84B', '#6CE9A6', '#DDD6FE', '#85E13A', '#5FE9D0', '#EEAAFD', '#FEA3B4'];

const bi_store = useBiStore();
const { chart_builder_data } = storeToRefs(bi_store);

const columns = computed(() => {
  return Object.keys(chart_builder_data.value[0]);
});

function onSeriesItemChange(payload, index) {
  emit('chartBuilderConfigChange', payload, index);
}

async function onAddSeries(index) {
  await nextTick();
  emit(
    'chartBuilderConfigChange',
    // Defaults
    {
      chart_type: 'bar',
      color: colors[index],
      y_axis: 'primary',
      line_style: 'solid',
      line_width: 1,
      line_shape: 'straight',
      prefix: '',
      suffix: '',
      stack: false,
    },
    index,
  );
}

function getAxis(type) {
  // type = 'category' || 'value'
  if (props.chartConfig.orientation === 'horizontal') {
    if (type === 'category')
      return 'y';
    else return 'x';
  }
  else {
    if (type === 'category')
      return 'x';
    else return 'y';
  }
}
</script>

<template>
  <template v-if="props.chartType === 'bar_chart'">
    <SelectElement
      name="layout_category"
      :label="getAxis('category') === 'x' ? 'X-axis' : 'Y-axis'"
      :items="columns"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <ListElement
      name="layout_values"
      :label="getAxis('value') === 'x' ? 'X-axis' : 'Y-axis'"
      :sort="true"
      :controls="{ add: true, remove: true, sort: true }"
      :add-classes="{ ListElement: { handle: 'left-8 top-[1px]' } }"
      add-text="+ Add another series"
      @add="onAddSeries"
    >
      <template #default="{ index }">
        <ObjectElement
          :name="index"
        >
          <SelectElement
            v-if="!props.chartConfig?.layout_values?.[index]?.value"
            name="value"
            :native="false"
            :items="columns"
            :add-classes="{
              SelectElement: {
                select: {
                  wrapper: 'ml-4',
                },
              },
            }"
          />
          <TextElement
            v-if="props.chartConfig?.layout_values?.[index]?.value"
            name="value"
            :disabled="true"
            :default=" props.chartConfig?.layout_values?.[index]?.value"
            :add-classes="{
              TextElement: {
                inputContainer: 'pl-6',
              },
            }"
          >
            <template #addon-before>
              <HawkMenu position="fixed" additional_trigger_classes="mt-1.5 p-0 m-0 !ring-0 !border-0 focus:!ring-0">
                <template #trigger>
                  <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: props.chartConfig?.layout_values?.[index].color }" />
                </template>
                <template #content>
                  <div class="flex flex-wrap gap-x-3 gap-y-2 w-[355px] p-3">
                    <div
                      v-for="color in colors"
                      :key="color"
                      class="p-0.5 rounded-full border border-transparent cursor-pointer"
                      :class="{ '!border-gray-300': props.chartConfig?.layout_values?.[index].color === color }"
                      @click="onSeriesItemChange({ color }, index)"
                    >
                      <div
                        class="w-4 h-4 rounded-full"
                        :style="{ backgroundColor: color }"
                      />
                    </div>
                  </div>
                </template>
              </HawkMenu>
            </template>
            <template #addon-after>
              <ChartBuilderSeriesContextMenu
                :series-config="props.chartConfig?.layout_values?.[index]"
                @field-selected="onSeriesItemChange($event, index)"
              />
            </template>
          </TextElement>
          <HiddenElement name="chart_type" />
          <HiddenElement name="y_axis" />
          <HiddenElement name="line_style" />
          <HiddenElement name="line_width" />
          <HiddenElement name="line_shape" />
          <HiddenElement name="prefix" />
          <HiddenElement name="suffix" />
          <HiddenElement name="stack" />
        </ObjectElement>
      </template>
    </ListElement>
    Stack by
  </template>
  <template v-else>
    Layout - {{ props.chartType }}
  </template>
</template>
